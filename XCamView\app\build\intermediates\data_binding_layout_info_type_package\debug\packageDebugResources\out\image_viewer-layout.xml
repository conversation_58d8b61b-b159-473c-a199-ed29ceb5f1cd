<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_viewer" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\image_viewer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/image_viewer_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="249" endOffset="13"/></Target><Target id="@+id/image_view" view="com.touptek.measurerealize.TpImageView"><Expressions/><location startLine="6" startOffset="4" endLine="10" endOffset="39"/></Target><Target id="@+id/measurement_overlay" view="com.touptek.measurerealize.utils.MeasurementOverlayView"><Expressions/><location startLine="13" startOffset="4" endLine="20" endOffset="35"/></Target><Target id="@+id/measurement_toolbar" view="LinearLayout"><Expressions/><location startLine="30" startOffset="8" endLine="217" endOffset="22"/></Target><Target id="@+id/btn_calibration" view="ImageButton"><Expressions/><location startLine="41" startOffset="12" endLine="47" endOffset="47"/></Target><Target id="@+id/btn_delete_measurement" view="ImageButton"><Expressions/><location startLine="49" startOffset="12" endLine="55" endOffset="47"/></Target><Target id="@+id/btn_angle" view="ImageButton"><Expressions/><location startLine="57" startOffset="12" endLine="63" endOffset="47"/></Target><Target id="@+id/btn_angle_three" view="ImageButton"><Expressions/><location startLine="65" startOffset="12" endLine="71" endOffset="47"/></Target><Target id="@+id/btn_point" view="ImageButton"><Expressions/><location startLine="73" startOffset="12" endLine="79" endOffset="47"/></Target><Target id="@+id/btn_arb_line" view="ImageButton"><Expressions/><location startLine="81" startOffset="12" endLine="87" endOffset="47"/></Target><Target id="@+id/btn_three_line" view="ImageButton"><Expressions/><location startLine="89" startOffset="12" endLine="95" endOffset="47"/></Target><Target id="@+id/btn_horizon_line" view="ImageButton"><Expressions/><location startLine="97" startOffset="12" endLine="103" endOffset="47"/></Target><Target id="@+id/btn_vertical_line" view="ImageButton"><Expressions/><location startLine="105" startOffset="12" endLine="111" endOffset="47"/></Target><Target id="@+id/btn_parallel_line" view="ImageButton"><Expressions/><location startLine="113" startOffset="12" endLine="119" endOffset="47"/></Target><Target id="@+id/btn_three_vertical" view="ImageButton"><Expressions/><location startLine="121" startOffset="12" endLine="127" endOffset="47"/></Target><Target id="@+id/btn_rectangle" view="ImageButton"><Expressions/><location startLine="129" startOffset="12" endLine="135" endOffset="47"/></Target><Target id="@+id/btn_three_rectangle" view="ImageButton"><Expressions/><location startLine="137" startOffset="12" endLine="143" endOffset="47"/></Target><Target id="@+id/btn_ellipse" view="ImageButton"><Expressions/><location startLine="145" startOffset="12" endLine="151" endOffset="47"/></Target><Target id="@+id/btn_five_ellipse" view="ImageButton"><Expressions/><location startLine="153" startOffset="12" endLine="159" endOffset="47"/></Target><Target id="@+id/btn_center_circle" view="ImageButton"><Expressions/><location startLine="161" startOffset="12" endLine="167" endOffset="47"/></Target><Target id="@+id/btn_three_circle" view="ImageButton"><Expressions/><location startLine="169" startOffset="12" endLine="175" endOffset="47"/></Target><Target id="@+id/btn_annulus" view="ImageButton"><Expressions/><location startLine="177" startOffset="12" endLine="183" endOffset="47"/></Target><Target id="@+id/btn_annulus2" view="ImageButton"><Expressions/><location startLine="185" startOffset="12" endLine="191" endOffset="47"/></Target><Target id="@+id/btn_twocircles" view="ImageButton"><Expressions/><location startLine="193" startOffset="12" endLine="199" endOffset="47"/></Target><Target id="@+id/btn_three_twocircles" view="ImageButton"><Expressions/><location startLine="201" startOffset="12" endLine="207" endOffset="47"/></Target><Target id="@+id/btn_arc" view="ImageButton"><Expressions/><location startLine="209" startOffset="12" endLine="215" endOffset="47"/></Target><Target id="@+id/button_panel" view="LinearLayout"><Expressions/><location startLine="221" startOffset="4" endLine="248" endOffset="18"/></Target><Target id="@+id/btn_previous" view="Button"><Expressions/><location startLine="231" startOffset="8" endLine="235" endOffset="38"/></Target><Target id="@+id/btn_next" view="Button"><Expressions/><location startLine="237" startOffset="8" endLine="241" endOffset="38"/></Target><Target id="@+id/btn_back" view="Button"><Expressions/><location startLine="243" startOffset="8" endLine="247" endOffset="35"/></Target></Targets></Layout>
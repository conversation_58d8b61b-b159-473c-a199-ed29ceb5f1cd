0com.touptek.measurerealize.utils.MeasurementDataandroidx.fragment.app.Fragment1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder(androidx.appcompat.app.AppCompatActivity6com.touptek.xcamview.view.MeasurementOverlayView.Shape$androidx.fragment.app.DialogFragmentandroid.view.Viewkotlin.EnumRcom.touptek.xcamview.activity.settings.TpMiscSettingsFragment.OnModeChangeListener&com.touptek.xcamview.util.BaseActivity-android.view.View.OnAttachStateChangeListenerDcom.touptek.xcamview.activity.MainMenu.OnRectangleVisibilityListener8androidx.recyclerview.widget.RecyclerView.ItemDecorationScom.touptek.xcamview.activity.browse.TpCopyDirDialogFragment.OnMoveCompleteListener,androidx.appcompat.widget.AppCompatImageView androidx.viewbinding.ViewBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
package com.touptek.xcamview.activity.measurement

import android.os.Bundle
import android.view.*
import com.touptek.xcamview.R
import android.widget.ImageButton
import android.graphics.drawable.Drawable
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.activity.MainActivity
import com.touptek.xcamview.view.MeasurementOverlayView
import kotlin.apply
import kotlin.collections.forEach
import kotlin.let
import kotlin.ranges.until


class TpMeasurementDialogFragment : DialogFragment() {
    private val buttons = mutableListOf<ImageButton>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle? // 关键修正点：Bundle 改为 Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.measurement_layout, container, false)
        setupButtons(view)
        return view
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            setDimAmount(0f)
//            setBackgroundDrawableResource(R.color.Light_black)
            setBackgroundDrawableResource(R.color.white_background)
            val params = attributes.apply {
                width = WindowManager.LayoutParams.WRAP_CONTENT
                height = WindowManager.LayoutParams.WRAP_CONTENT
            }

            arguments?.let { args ->
                val marginPx = 20
                val anchorX = args.getInt("anchor_x")
                val anchorWidth = args.getInt("anchor_width")

                // 横坐标保持不变的计算方式
                val targetX = anchorX + anchorWidth + marginPx

                // 纵坐标置顶（距离屏幕顶部20px）
                val targetY = 0  // 可根据需要调整这个边距值

                params.gravity = Gravity.TOP or Gravity.START
                params.x = targetX
                params.y = targetY
            }

            attributes = params
        }
    }


    private fun setupButtons(view: View) {
        val root = view as ViewGroup
        for (i in 0 until root.childCount) {
            val child = root.getChildAt(i)
            if (child is ImageButton) {
                buttons.add(child)
                child.tag = child.background
                child.setOnClickListener { toggleButtonState(child) }
            }
        }
    }

    private fun toggleButtonState(selectedButton: ImageButton) {
        buttons.forEach { button ->
            if (button == selectedButton) {
                val wasSelected = button.isSelected
                if (wasSelected) {
                    button.background = button.tag as Drawable
                    button.isSelected = false
                } else {
                    button.setBackgroundResource(R.drawable.grey_background)
                    button.isSelected = true
                }

                // 处理特定按钮点击
                when (button.id) {
                    R.id.btn_angle-> handleAngleClick(!wasSelected)
                    R.id.btn_angle_three -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_point -> handlePointClick(!wasSelected)
                    R.id.btn_arb_line -> handleArbClick(!wasSelected)
                    R.id.btn_three_line -> handleThreeLineClick(!wasSelected)
                    R.id.btn_horizon_line -> handleHorizonLineClick(!wasSelected)
                    R.id.btn_vertical_line -> handleVerticalLineClick(!wasSelected)
                    R.id.btn_parallel_line -> handleParallelClick(!wasSelected)
                    R.id.btn_three_vertical -> handleThreeVerticalClick(!wasSelected)

                    R.id.btn_rectangle-> handleRectangleClick(!wasSelected)
                    R.id.btn_three_rectangle -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_ellipse -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_five_ellipse -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_center_circle -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_three_circle -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_annulus -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_annulus2 -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_twocircles -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_three_twocircles -> handleAngleThreeClick(!wasSelected)
                    R.id.btn_arc -> handleAngleThreeClick(!wasSelected)
                }
            } else {
                button.background = button.tag as Drawable
                button.isSelected = false
            }
        }
    }

    private fun handleAngleClick(isSelected: Boolean) {
        // 获取父Fragment（TpImageDecodeDialogFragment）
        val parentFragment = parentFragmentManager.fragments.find {
            it is com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment
        } as? com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment

        if (isSelected) {
            println("Angle measurement started...")
            parentFragment?.startAngleMeasurement()
        } else {
            println("Angle measurement stopped")
            parentFragment?.stopAngleMeasurement()
        }
    }

    private fun handleAngleThreeClick(isSelected: Boolean) {
        if (isSelected) {
            println("AngleThree")
            // 在这里添加校准初始化逻辑
        } else {
            println("AngleThree false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handlePointClick(isSelected: Boolean) {
        if (isSelected) {
            println("Point")
            // 在这里添加校准初始化逻辑
        } else {
            println("Point false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handleArbClick(isSelected: Boolean) {
        if (isSelected) {
            println("Point")
            // 在这里添加校准初始化逻辑
        } else {
            println("Point false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handleThreeLineClick(isSelected: Boolean) {
        if (isSelected) {
            println("Point")
            // 在这里添加校准初始化逻辑
        } else {
            println("Point false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handleHorizonLineClick(isSelected: Boolean) {
        (activity as? MainActivity)?.let { activity ->
            if (isSelected) {
                activity.showMeasurementView()
                activity.setMeasurementMode(MeasurementOverlayView.Mode.LINE)
            } else {
                activity.hideMeasurementView()
            }
        }
    }

    private fun handleVerticalLineClick(isSelected: Boolean) {
        if (isSelected) {
            println("Point")
            // 在这里添加校准初始化逻辑
        } else {
            println("Point false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handleParallelClick(isSelected: Boolean) {
        if (isSelected) {
            println("Point")
            // 在这里添加校准初始化逻辑
        } else {
            println("Point false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handleThreeVerticalClick(isSelected: Boolean) {
        if (isSelected) {
            println("Point")
            // 在这里添加校准初始化逻辑
        } else {
            println("Point false")
            // 在这里添加校准取消逻辑
        }
    }

    private fun handleRectangleClick(isSelected: Boolean) {
        if (isSelected) {
            showToast("Rectangle Paint")
            // 在这里添加校准初始化逻辑
        } else {
            showToast("Rectangle False")
            // 在这里添加校准取消逻辑
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
    }
}
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- 图片显示区域 -->
    <com.touptek.measurerealize.TpImageView
        android:id="@+id/image_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="fitCenter" />

    <!-- 测量覆盖层 -->
    <com.touptek.measurerealize.utils.MeasurementOverlayView
        android:id="@+id/measurement_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone" />

    <!-- 🎯 顶部测量工具栏 - 直接嵌入布局，包含完整的23个按钮，居中显示 -->
    <HorizontalScrollView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="top|center_horizontal"
        android:layout_marginTop="20dp"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/measurement_toolbar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="0dp"
            android:paddingHorizontal="8dp"
            android:background="@color/white_background"
            android:visibility="visible"
            android:layout_gravity="center_horizontal">

            <ImageButton
                android:id="@+id/btn_calibration"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_calibration_n"
                android:contentDescription="@string/button_desc_calibration"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_delete_measurement"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_delete_n"
                android:contentDescription="@string/button_desc_delete_measurement"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_angle"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_angle_n"
                android:contentDescription="@string/button_desc_angle"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_angle_three"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_fourptangle_n"
                android:contentDescription="@string/button_desc_angle_three"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_point"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_point_n"
                android:contentDescription="@string/button_desc_point"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_arb_line"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_arbline_n"
                android:contentDescription="@string/button_desc_arb_line"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_three_line"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_threeline_n"
                android:contentDescription="@string/button_desc_three_line"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_horizon_line"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_hline_n"
                android:contentDescription="@string/button_desc_horizon_line"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_vertical_line"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_vline_n"
                android:contentDescription="@string/button_desc_vertical_line"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_parallel_line"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_parallel_n"
                android:contentDescription="@string/button_desc_parallel_line"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_three_vertical"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_threevertical_n"
                android:contentDescription="@string/button_desc_three_vertical"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_rectangle"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_rectangle_n"
                android:contentDescription="@string/button_desc_rectangle"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_three_rectangle"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_threerectangle_n"
                android:contentDescription="@string/button_desc_three_rectangle"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_ellipse"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_ellipse_n"
                android:contentDescription="@string/button_desc_ellipse"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_five_ellipse"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_fiveellipse_n"
                android:contentDescription="@string/button_desc_five_ellipse"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_center_circle"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_centerc_n"
                android:contentDescription="@string/button_desc_center_circle"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_three_circle"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_threecircle_n"
                android:contentDescription="@string/button_desc_three_circle"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_annulus"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_annulus_n"
                android:contentDescription="@string/button_desc_annulus"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_annulus2"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_annulus2_n"
                android:contentDescription="@string/button_desc_annulus2"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_twocircles"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_angle_n"
                android:contentDescription="@string/button_desc_twocircles"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_three_twocircles"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_threepttwowircles_n"
                android:contentDescription="@string/button_desc_three_twocircles"
                android:layout_marginEnd="8dp"/>

            <ImageButton
                android:id="@+id/btn_arc"
                android:layout_width="64dp"
                android:layout_height="64dp"
                android:background="@drawable/ic_arc_n"
                android:contentDescription="@string/button_desc_arc"
                android:layout_marginEnd="8dp"/>

        </LinearLayout>
    </HorizontalScrollView>

    <!-- 底部按钮区域 -->
    <LinearLayout
        android:id="@+id/button_panel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="16dp"
        android:background="#80000000">

        <Button
            android:id="@+id/btn_previous"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="上一张" />

        <Button
            android:id="@+id/btn_next"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="下一张" />

        <Button
            android:id="@+id/btn_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="返回" />
    </LinearLayout>
</FrameLayout>
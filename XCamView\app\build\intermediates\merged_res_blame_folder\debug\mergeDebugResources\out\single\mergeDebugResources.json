[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_white_balance_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_white_balance_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_close.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_close.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_zoom_out_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_zoom_out_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_wb_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_wb_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_fragment_format_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_format_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_freeze_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\freeze_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_config_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\config_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_annulus2_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_annulus2_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_folder_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_folder_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_popup_config_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\popup_config_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_action3.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_action3.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_vline_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_vline_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_record_video.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_record_video.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_text_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_text_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_settings_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_settings_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_selector_settings_tab.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\selector_settings_tab.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_measure_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\measure_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_dialog_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\dialog_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_divider_vertical_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\divider_vertical_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_image_border_normal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\image_border_normal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_config_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_config_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_threevertical_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_threevertical_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_activity_welcome.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_welcome.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_video_triangle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_video_triangle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_whitebalance_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\whitebalance_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_hz_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\hz_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_action4.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_action4.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_groupbox_title_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\groupbox_title_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_pause.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_pause.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_isp_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\isp_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_divider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\divider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_record_video_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_record_video_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_action5.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_action5.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_thumb_blue_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\thumb_blue_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_selector_tab_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\selector_tab_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_scenechange_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\scenechange_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_fold_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_fold_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_testdialog_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\testdialog_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_threepttwowircles_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_threepttwowircles_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_zoomout_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\zoomout_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_status_banner_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\status_banner_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_exposure_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\exposure_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_about_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_about_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_image_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\image_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_add_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\add_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_about_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\about_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_folder_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_folder_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_action2.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_action2.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_delete_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_delete_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_image_parameter_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\image_parameter_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_unchecked.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_unchecked.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_imageprocess_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\imageprocess_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_paste.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_paste.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\font_kai.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\font\\kai.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_pause_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_pause_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_zoom_out_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_zoom_out_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_rectangle_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_rectangle_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-xxxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_allselect.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_allselect.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_power_frequency.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_power_frequency.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_video_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\video_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_config_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\config_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_checked.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_checked.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_draw.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_draw.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_pic.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_pic.png"}, {"merged": "com.touptek.xcamview.app-debug-41:/layout_image_viewer.xml.flat", "source": "com.touptek.xcamview.app-main-43:/layout/image_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_polygon_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_polygon_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tp_custom_seekbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tp_custom_seekbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_take_photo_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_take_photo_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_autoae_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\autoae_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_hz_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\hz_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tp_switch_track.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tp_switch_track.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tp_custom_radionbutton.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tp_custom_radionbutton.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_threecircle_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_threecircle_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_thumb_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\thumb_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_menu_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_menu_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_operation_grid_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\operation_grid_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_folder_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\folder_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_action1.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_action1.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_annotation_arrow_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_annotation_arrow_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_annulus_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_annulus_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_hline_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_hline_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_next_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\next_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_zoom_in.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_zoom_in.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_grey_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\grey_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_record_video_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_record_video_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\color_red.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\color\\red.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_zoomout_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\zoomout_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_flip_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_flip_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_zoom_in_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_zoom_in_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-xhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-xhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_fragment_network_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_network_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_record_start_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\record_start_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_lock_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_lock_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_threeline_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_threeline_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_menu_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_menu_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_dialog_file_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\dialog_file_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_flip.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_flip.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_measurement_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\measurement_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_preview.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_preview.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_activity_touptek.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_touptek.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_snap_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\snap_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_draw_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_draw_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_about.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_about.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_fragment_storage_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_storage_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_delete_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\delete_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_exposure_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_exposure_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_white_balance_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_white_balance_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tp_switch_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tp_switch_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_zoom_out.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_zoom_out.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_bg_rounded_dialog_light.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\bg_rounded_dialog_light.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_cut.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_cut.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_confirm_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_confirm_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_white_balance.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_white_balance.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_arc_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_arc_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\color_tab_text_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\color\\tab_text_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_fiveellipse_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_fiveellipse_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_copydialog_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\copydialog_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_activity_touptek_btn.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\activity_touptek_btn.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_parallel_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_parallel_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-xhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-xhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_draw_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_draw_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-hdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-hdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_color_adjustment_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_color_adjustment_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_exposure_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_exposure_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_white.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_white.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_calibration_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_calibration_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_bottom_panel_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\bottom_panel_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_popup_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\popup_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_fourptangle_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_fourptangle_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_cancel_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_cancel_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_freeze_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\freeze_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_right_panel_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\right_panel_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-xxhdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_settings.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_settings.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_nav_separator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\nav_separator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_sub_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\sub_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_oval_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\oval_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_zoomin_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\zoomin_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-hdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-hdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_settings_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_settings_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_settings_record.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\settings_record.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_browser_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\browser_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_fragment_measurement_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\fragment_measurement_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-xxxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-xxxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_rounded_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\rounded_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tp_custom_seekbar_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tp_custom_seekbar_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_fold.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_fold.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_arrowleft.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_arrowleft.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_about_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_about_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_image_parameter_2_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\image_parameter_2_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_flip_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_flip_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_storage.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_storage.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_button_border_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\button_border_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_color_adjust.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_color_adjust.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_arbline_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_arbline_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_centerc_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_centerc_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_folder.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_folder.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_menu.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_menu.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_flip_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\flip_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-mdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-mdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_video.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_video.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_settings_misc.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\settings_misc.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_arrowright_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_arrowright_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_videodecode_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\videodecode_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_image_processing_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_image_processing_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_select_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_select_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_power_frequency_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_power_frequency_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_return.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_return.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_ellipse_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_ellipse_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_browse_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\browse_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tab_selected_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tab_selected_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_color_adjustment.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_color_adjustment.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_record_start.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_record_start.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_brow_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\brow_d.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-xxhdpi_ic_launcher.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-xxhdpi\\ic_launcher.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_dialog_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\dialog_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_zoom_in_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_zoom_in_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_image_processing_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_image_processing_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_close_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_close_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\mipmap-mdpi_ic_launcher_round.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\mipmap-mdpi\\ic_launcher_round.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_popup_menu_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\popup_menu_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_pause_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_pause_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_border_box.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\border_box.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_cancel.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_cancel.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_exposure.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_exposure.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_point_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_point_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_power_frequency_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_power_frequency_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_take_photo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_take_photo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_groupbox_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\groupbox_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_home_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\home_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_zoomin_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\zoomin_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_image_processing.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_image_processing.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_rounded_border.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\rounded_border.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_browse_grid_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\browse_grid_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_dialog_modern_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\dialog_modern_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_track_blue_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\track_blue_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_flip_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\flip_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_stepframe_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\stepframe_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_export_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_export_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_tp_custom_enabled_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\tp_custom_enabled_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_copy.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_copy.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\font_song.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\font\\song.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_scenechange_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\scenechange_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_color_adjustment_pressed.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_color_adjustment_pressed.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_btn_take_photo_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\btn_take_photo_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_title_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\title_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_picture.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_picture.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\layout_layout_input_info_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\layout\\layout_input_info_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_threerectangle_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_threerectangle_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_angle_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_angle_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_ic_scale_bar_n.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\ic_scale_bar_n.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-debug-41:\\drawable_about_d.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.7\\com.touptek.xcamview.app-main-43:\\drawable\\about_d.png"}]
package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎨 测量数据基类 - 专业级测量系统
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001B\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\t\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\u00a8\u0006\f"}, d2 = {"Lcom/touptek/measurerealize/utils/MeasurementData;", "", "()V", "Lcom/touptek/measurerealize/utils/AngleMeasurementData;", "Lcom/touptek/measurerealize/utils/CircleMeasurementData;", "Lcom/touptek/measurerealize/utils/DistanceMeasurementData;", "Lcom/touptek/measurerealize/utils/EllipseMeasurementData;", "Lcom/touptek/measurerealize/utils/FourPointAngleMeasurementData;", "Lcom/touptek/measurerealize/utils/MultiPointPathMeasurementData;", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasurementData;", "Lcom/touptek/measurerealize/utils/RectangleMeasurementData;", "Lcom/touptek/measurerealize/utils/ThreePointCircleMeasurementData;", "app_debug"})
public abstract class MeasurementData {
    
    private MeasurementData() {
        super();
    }
}
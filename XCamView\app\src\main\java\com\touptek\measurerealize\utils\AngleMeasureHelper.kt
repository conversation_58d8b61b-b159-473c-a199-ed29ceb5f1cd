package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import android.widget.ImageView
import android.widget.TextView
import kotlin.math.*
import android.view.View
import com.touptek.measurerealize.TpImageView
import java.util.*
import kotlin.collections.ArrayList

/**
 * 🎨 专业级角度测量助手 - 超越iPad体验的交互设计
 *
 * 核心特性：
 * - 多层测量管理：支持无限个角度测量同时存在
 * - 智能交互：点击创建、拖拽编辑、长按删除
 * - 专业视觉：动态高亮、流畅动画、精美渲染
 * - 完美集成：与TpImageView缩放功能无缝协作
 * - 暂停/恢复：支持暂停测量并重新激活编辑
 */

/**
 * 🎨 专业级角度测量实例 - 绘图应用级别的体验
 */
data class AngleMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // 🎯 存储视图坐标（顶点+两个端点）
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // 🎯 存储位图坐标（用于保存）
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {

    /**
     * 🎨 计算角度值（使用位图坐标 - 真实角度，不受缩放影响）
     */
    fun calculateAngle(): Double {
        if (bitmapPoints.size < 3) return 0.0

        val vertex = bitmapPoints[0]    // 顶点
        val point1 = bitmapPoints[1]    // 第一个点
        val point2 = bitmapPoints[2]    // 第二个点

        // 🎯 使用向量计算角度
        val vector1 = PointF(point1.x - vertex.x, point1.y - vertex.y)
        val vector2 = PointF(point2.x - vertex.x, point2.y - vertex.y)

        // 计算向量的模长
        val length1 = sqrt(vector1.x * vector1.x + vector1.y * vector1.y)
        val length2 = sqrt(vector2.x * vector2.x + vector2.y * vector2.y)

        if (length1 == 0f || length2 == 0f) return 0.0

        // 计算点积
        val dotProduct = vector1.x * vector2.x + vector1.y * vector2.y

        // 计算角度（弧度）
        val cosAngle = dotProduct / (length1 * length2)
        val angleRadians = acos(cosAngle.coerceIn((-1.0).toFloat(), 1.0F))

        // 转换为度数
        return Math.toDegrees(angleRadians.toDouble())
    }

    /**
     * 🎯 检查点是否在触摸范围内 - 直接在视图坐标系中检测
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex >= viewPoints.size) return false
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }

    /**
     * 🎨 更新视图坐标中的点位置（专业绘图应用体验）
     */
    fun updateViewPoint(pointIndex: Int, newViewPoint: PointF) {
        if (pointIndex < viewPoints.size) {
            viewPoints[pointIndex] = newViewPoint
            markAsModified()
        }
    }

    /**
     * 🔄 同步位图坐标（当需要保存时调用）- 添加合理性检查
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEachIndexed { index, viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)

            // 添加坐标合理性检查
            val isReasonable = bitmapPoint.x >= -10000 && bitmapPoint.x <= 10000 &&
                              bitmapPoint.y >= -10000 && bitmapPoint.y <= 10000

            if (!isReasonable) {
                android.util.Log.w("AngleMeasurement", "⚠️ Unreasonable bitmap coords: view($viewPoint) -> bitmap($bitmapPoint)")
                // 使用视图坐标作为fallback
                bitmapPoints.add(PointF(viewPoint.x, viewPoint.y))
            } else {
                bitmapPoints.add(bitmapPoint)
                android.util.Log.d("AngleMeasurement", "✅ Coord[$index]: view($viewPoint) -> bitmap($bitmapPoint)")
            }
        }
    }

    /**
     * 🔄 同步视图坐标（缩放变化时调用）
     */
    fun syncViewCoords(imageView: ImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
    }

    private fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }
}

/**
 * 🎨 专业级角度测量助手 - 核心控制器
 */
class AngleMeasureHelper {
    companion object {
        private const val TAG = "AngleMeasureHelper"
        
        // 交互参数 - 精心调优的专业级参数
        private const val POINT_RADIUS = 14f           // 点的基础半径
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径（更大的触摸区域）
        private const val HIGHLIGHT_RADIUS = 18f       // 高亮时的点半径
        private const val DRAG_RADIUS = 22f            // 拖拽时的点半径
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间
        private const val CLICK_TOLERANCE = 20f        // 点击容差（与yolo_demo一致）

        // 颜色方案 - 专业设计师级配色
        private val COLOR_VERTEX = Color.parseColor("#E91E63")        // 顶点颜色（粉色）
        private val COLOR_POINT1 = Color.parseColor("#4CAF50")        // 第一个点颜色（绿色）
        private val COLOR_POINT2 = Color.parseColor("#FF5722")        // 第二个点颜色（橙红色）
        private val COLOR_LINE = Color.parseColor("#2196F3")          // 线条颜色（蓝色）
        private val COLOR_ARC = Color.parseColor("#FF9800")           // 弧线颜色（橙色）
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var textView: TextView
    private lateinit var originalBitmap: Bitmap

    // 测量数据管理
    private val measurements = ArrayList<AngleMeasurement>()
    private var activeMeasurement: AngleMeasurement? = null
    private var selectedMeasurement: AngleMeasurement? = null

    // 交互状态
    private var isDraggingPoint = false
    private var isDraggingText = false
    private var draggedPointIndex = -1
    private var isCreatingNew = false
    private var longPressStartTime = 0L

    // 触摸距离检测（完全复制yolo_demo）
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调函数
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化测量助手 - 专业级初始化流程
     */
    fun init(imageView: TpImageView, textView: TextView, bitmap: Bitmap) {
        this.imageView = imageView
        this.textView = textView
        this.originalBitmap = bitmap

        setupTouchListener()
        Log.d(TAG, "🚀 AngleMeasureHelper initialized with professional-grade interaction")
    }

    /**
     * 🎯 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎨 开始新的角度测量 - 在屏幕中心生成可拖动的角度
     */
    fun startNewMeasurement(): String {
        Log.d(TAG, "🚀 [DEBUG] Starting new measurement - imageView size: ${imageView.width}x${imageView.height}")

        // 🎯 在视图中心生成角度（完全复制yolo_demo逻辑）
        val viewCenterX = imageView.width / 2f
        val viewCenterY = imageView.height / 2f
        val armLength = minOf(imageView.width, imageView.height) / 8f // 与yolo_demo一致：/8f

        Log.d(TAG, "🎯 [DEBUG] Calculated center: ($viewCenterX, $viewCenterY), armLength: $armLength")

        // 🎨 创建60度角（与yolo_demo完全一致）
        val baseAngle = 0.0 // 水平向右
        val angleSpread = 60.0 // 60度张开

        val angle1Rad = Math.toRadians(baseAngle)
        val angle2Rad = Math.toRadians(baseAngle + angleSpread)

        val vertex = PointF(viewCenterX, viewCenterY)
        val point1 = PointF(
            viewCenterX + (cos(angle1Rad) * armLength).toFloat(),
            viewCenterY - (sin(angle1Rad) * armLength).toFloat()
        )
        val point2 = PointF(
            viewCenterX + (cos(angle2Rad) * armLength).toFloat(),
            viewCenterY - (sin(angle2Rad) * armLength).toFloat()
        )

        Log.d(TAG, "🎯 Generated angle: center($viewCenterX, $viewCenterY), armLength=$armLength")
        Log.d(TAG, "🎯 Points: vertex=$vertex, point1=$point1, point2=$point2")

        // 创建完整的测量（与yolo_demo完全一致）
        val measurement = AngleMeasurement(
            isSelected = true,
            isEditing = false,  // 直接设为完成状态，可拖动
            isCompleted = true
        )

        Log.d(TAG, "📊 [DEBUG] Created measurement object with ID: ${measurement.id}")

        // 🎨 添加视图坐标点
        measurement.viewPoints.addAll(listOf(vertex, point1, point2))
        Log.d(TAG, "📊 [DEBUG] Added viewPoints: ${measurement.viewPoints.size} points")
        measurement.viewPoints.forEachIndexed { index, point ->
            Log.d(TAG, "📊 [DEBUG] viewPoint[$index]: $point")
        }

        // 🔄 同步位图坐标
        measurement.syncBitmapCoords(imageView)
        Log.d(TAG, "📊 [DEBUG] Synced bitmapPoints: ${measurement.bitmapPoints.size} points")
        measurement.bitmapPoints.forEachIndexed { index, point ->
            Log.d(TAG, "📊 [DEBUG] bitmapPoint[$index]: $point")
        }

        measurement.textPosition = PointF(vertex.x + 60f, vertex.y - 40f)

        // 取消其他测量的选中状态
        measurements.forEach {
            it.isSelected = false
            it.isEditing = false
        }

        Log.d(TAG, "📊 [DEBUG] Before adding: measurements.size = ${measurements.size}")
        measurements.add(measurement)
        Log.d(TAG, "📊 [DEBUG] After adding: measurements.size = ${measurements.size}")

        selectedMeasurement = measurement
        Log.d(TAG, "📊 [DEBUG] Selected measurement set to: ${selectedMeasurement?.id}")

        notifyUpdate()
        Log.d(TAG, "🎯 Generated draggable angle at screen center: ${measurement.calculateAngle()}°")
        Log.d(TAG, "✅ [DEBUG] startNewMeasurement completed successfully")

        return measurement.id
    }

    /**
     * 🎨 设置触摸监听器 - 专业级手势识别
     * 注意：不直接设置OnTouchListener，而是通过覆盖层处理触摸事件
     */
    private fun setupTouchListener() {
        // 不在这里设置触摸监听器，避免与TpImageView的手势检测冲突
        // 触摸事件将通过MeasurementOverlayView处理
        Log.d(TAG, "🎨 Touch listener setup completed (delegated to overlay)")
    }

    /**
     * 🎯 处理触摸事件 - 完全复制yolo_demo签名
     */
    @Suppress("UNUSED_PARAMETER")
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        Log.d(TAG, "🎯 Touch event: action=${event.action}, point=($x, $y), isDragging=$isDraggingPoint")

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                longPressStartTime = System.currentTimeMillis()
                // 记录触摸起始位置（完全复制yolo_demo）
                lastTouchX = x
                lastTouchY = y

                // 检查是否点击了现有测量的控制点
                val (measurement, pointIndex) = findTouchedPoint(touchPoint)
                Log.d(TAG, "🔍 Found touched point: measurement=${measurement?.id}, pointIndex=$pointIndex")

                if (measurement != null && pointIndex != -1) {
                    selectedMeasurement = measurement
                    draggedPointIndex = pointIndex
                    isDraggingPoint = true

                    // 设置选中状态
                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true

                    Log.d(TAG, "🎯 Started dragging point $pointIndex of measurement ${measurement.id}")
                    notifyUpdate()
                    return true
                }

                // 🎯 问题2修复：点击空白区域时，清除所有选中状态
                // 但是要确保真的是点击了远离所有角度的空白区域
                if (measurements.isNotEmpty()) {
                    // 检查是否点击在任何角度的附近区域
                    val isNearAnyMeasurement = measurements.any { measurement ->
                        measurement.viewPoints.any { point ->
                            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
                            distance <= 100f // 100像素的容忍范围
                        }
                    }

                    if (!isNearAnyMeasurement) {
                        Log.d(TAG, "🔄 Clearing all selections - clicked on empty area far from measurements")
                        measurements.forEach { it.isSelected = false }
                        selectedMeasurement = null
                        notifyUpdate()
                    } else {
                        Log.d(TAG, "🎯 Click near measurement - keeping current selection")
                    }
                }

                Log.d(TAG, "❌ No point found, not handling touch")
                return false
            }

            MotionEvent.ACTION_MOVE -> {
                Log.d(TAG, "📱 ACTION_MOVE: isDragging=$isDraggingPoint, selected=${selectedMeasurement?.id}, dragIndex=$draggedPointIndex, measurements=${measurements.size}")

                if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex != -1) {
                    // 🎨 正常拖拽控制点
                    selectedMeasurement!!.updateViewPoint(draggedPointIndex, touchPoint)
                    selectedMeasurement!!.syncBitmapCoords(imageView)

                    Log.d(TAG, "✅ Point updated to ($x, $y)")
                    notifyUpdate()
                    return true
                } else if (!isDraggingPoint && selectedMeasurement != null) {
                    // 🔄 触摸事件恢复机制：尝试重新建立拖拽关系
                    Log.d(TAG, "🔄 Attempting touch recovery - checking if point is near measurement")

                    val (measurement, pointIndex) = findTouchedPoint(touchPoint)
                    if (measurement != null && pointIndex != -1 && measurement == selectedMeasurement) {
                        // 重新建立拖拽关系
                        draggedPointIndex = pointIndex
                        isDraggingPoint = true

                        Log.d(TAG, "🔄 Touch recovery successful - resumed dragging point $pointIndex")

                        // 立即处理这次移动
                        selectedMeasurement!!.updateViewPoint(draggedPointIndex, touchPoint)
                        selectedMeasurement!!.syncBitmapCoords(imageView)
                        notifyUpdate()
                        return true
                    }
                } else {
                    // 🎯 问题1修复：删除所有角度后，不自动创建新角度
                    // 只有通过按钮点击才能创建新角度
                    Log.d(TAG, "❌ No measurements available and not creating auto-measurement")
                }

                Log.d(TAG, "❌ ACTION_MOVE not handled - no recovery possible")
                return false
            }

            MotionEvent.ACTION_UP -> {
                val currentTime = System.currentTimeMillis()
                val touchDuration = currentTime - longPressStartTime
                val touchDistance = kotlin.math.sqrt((x - lastTouchX) * (x - lastTouchX) + (y - lastTouchY) * (y - lastTouchY))
                val wasDragging = isDraggingPoint

                Log.d(TAG, "🔚 ACTION_UP: wasDragging=$wasDragging, duration=$touchDuration, distance=$touchDistance")
                Log.d(TAG, "🔚 Before UP - selected: ${selectedMeasurement?.id}, measurements count: ${measurements.size}")

                var handled = false

                // 重置拖拽状态
                isDraggingPoint = false
                isDraggingText = false
                draggedPointIndex = -1

                if (wasDragging) {
                    // 正常的拖拽结束 - 保持所有数据
                    Log.d(TAG, "✅ Normal drag completed - preserving measurement data")
                    notifyUpdate()
                    handled = true
                }

                // 🎯 完全复制yolo_demo的长按删除逻辑
                if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    // 查找被长按的测量点
                    val (measurement, pointIndex) = findTouchedPoint(touchPoint)
                    if (measurement != null) {
                        measurements.remove(measurement)
                        Log.d(TAG, "🗑️ Long-press: Deleted measurement ${measurement.id}")
                        if (selectedMeasurement == measurement) {
                            selectedMeasurement = null
                        }
                        notifyUpdate()
                        handled = true
                    }
                }

                // 保持选中状态和测量数据（已在上面处理）

                Log.d(TAG, "🔚 After UP - selected: ${selectedMeasurement?.id}, measurements count: ${measurements.size}")
                Log.d(TAG, "🔚 Touch sequence ended, handled: $handled, data preserved: ${selectedMeasurement != null}")
                return handled
            }
        }
        
        return false
    }

    /**
     * 🔍 检测触摸点是否在测量点位上 - 公共方法供MeasurementOverlay使用
     */
    fun isTouchingMeasurementPoint(touchPoint: PointF): Boolean {
        val (measurement, pointIndex) = findTouchedPoint(touchPoint)
        val result = measurement != null && pointIndex != -1
        Log.d(TAG, "🔍 Touch detection at (${touchPoint.x}, ${touchPoint.y}): $result")
        if (result) {
            Log.d(TAG, "🎯 Found measurement point: ${measurement?.id}, index: $pointIndex")
        }
        return result
    }

    /**
     * 🔍 检查是否正在拖拽测量点
     */
    fun isDraggingPoint(): Boolean {
        return isDraggingPoint
    }

    /**
     * 🔍 查找被触摸的点
     */
    private fun findTouchedPoint(touchPoint: PointF): Pair<AngleMeasurement?, Int> {
        for (measurement in measurements.reversed()) { // 从最新的开始检查
            for (i in measurement.viewPoints.indices) {
                if (measurement.isPointInTouchRange(touchPoint, i, TOUCH_RADIUS)) {
                    return Pair(measurement, i)
                }
            }
        }
        return Pair(null, -1)
    }

    /**
     * 🎨 获取所有测量数据用于覆盖层显示 - 使用视图坐标
     */
    fun getAllMeasurementData(): List<AngleMeasurementData> {
        Log.d(TAG, "📊 [DEBUG] getAllMeasurementData called - measurements.size = ${measurements.size}")

        measurements.forEachIndexed { index, measurement ->
            Log.d(TAG, "📊 [DEBUG] measurement[$index]: id=${measurement.id}, viewPoints.size=${measurement.viewPoints.size}, isSelected=${measurement.isSelected}")
            Log.d(TAG, "📊 [DEBUG] measurement[$index]: angle=${measurement.calculateAngle()}°, isCompleted=${measurement.isCompleted}")
            measurement.viewPoints.forEachIndexed { pointIndex, point ->
                Log.d(TAG, "📊 [DEBUG] measurement[$index].viewPoint[$pointIndex]: $point")
            }
        }

        val result = measurements.mapNotNull { measurement ->
            if (measurement.viewPoints.size >= 3) {
                val angleMeasurementData = AngleMeasurementData(
                    points = measurement.viewPoints.toList(),
                    angle = measurement.calculateAngle(),
                    isDragging = isDraggingPoint && measurement.isSelected,
                    isSelected = measurement.isSelected
                )
                Log.d(TAG, "📊 [DEBUG] Created AngleMeasurementData: points.size=${angleMeasurementData.points.size}, angle=${angleMeasurementData.angle}°, isDragging=${angleMeasurementData.isDragging}")
                angleMeasurementData
            } else {
                Log.w(TAG, "⚠️ [DEBUG] Skipping measurement with insufficient points: ${measurement.viewPoints.size}")
                null
            }
        }

        Log.d(TAG, "📊 [DEBUG] getAllMeasurementData: returning ${result.size} measurements, selected: ${selectedMeasurement?.id}")
        Log.d(TAG, "📊 [DEBUG] isDraggingPoint: $isDraggingPoint, draggedPointIndex: $draggedPointIndex")

        return result
    }

    /**
     * 🔄 缩放变化时同步坐标 - 专业绘图应用体验
     */
    fun onScaleChanged() {
        Log.d(TAG, "🔄 Scale changed - syncing ${measurements.size} measurements")
        // 从位图坐标恢复视图坐标，确保测量跟随图像
        measurements.forEach { measurement ->
            Log.d(TAG, "🔄 Before sync: viewPoints size=${measurement.viewPoints.size}")
            measurement.syncViewCoords(imageView)
            Log.d(TAG, "🔄 After sync: viewPoints updated for measurement ${measurement.id}")
        }
        notifyUpdate()
        Log.d(TAG, "🔄 Coordinate sync completed")
    }

    /**
     * ⏸️ 暂停测量 - 保留所有数据和状态
     */
    fun pauseMeasurement() {
        Log.d(TAG, "⏸️ Pausing measurement - before: selected=${selectedMeasurement?.id}, count=${measurements.size}")

        // 暂停编辑状态，但严格保留所有数据
        isDraggingPoint = false
        isDraggingText = false
        draggedPointIndex = -1
        isCreatingNew = false

        // 关键：绝对不清除测量数据和选中状态
        // selectedMeasurement 和 measurements 保持不变

        Log.d(TAG, "⏸️ Measurement paused - after: selected=${selectedMeasurement?.id}, count=${measurements.size}")
        Log.d(TAG, "⏸️ Data preserved successfully")
    }

    /**
     * ▶️ 恢复测量 - 从暂停状态恢复到可编辑状态
     */
    fun resumeMeasurement() {
        // 恢复编辑状态，所有数据保持不变
        // 用户可以继续编辑之前的测量
        Log.d(TAG, "▶️ Measurement resumed")
    }

    /**
     * 🧹 清除所有测量
     */
    fun clearAllMeasurements() {
        measurements.clear()
        selectedMeasurement = null
        activeMeasurement = null
        isDraggingPoint = false
        isDraggingText = false
        draggedPointIndex = -1
        notifyUpdate()
        Log.d(TAG, "🧹 All measurements cleared")
    }

    /**
     * 🎯 添加新的角度测量 - 公共方法，供MeasurementManager调用
     */
    fun addNewMeasurement(): String {
        return startNewMeasurement()
    }

    /**
     * 🗑️ 删除选中的角度测量
     */
    fun deleteSelectedMeasurement(): Boolean {
        val selected = selectedMeasurement
        Log.d(TAG, "🔍 deleteSelectedMeasurement called - selectedMeasurement: ${selected?.id}")
        Log.d(TAG, "🔍 Current measurements count: ${measurements.size}")
        Log.d(TAG, "🔍 Measurements list: ${measurements.map { "${it.id}(selected=${it.isSelected})" }}")

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurement selected for deletion")
            return false
        }

        Log.d(TAG, "🗑️ Deleting selected measurement: ${selected.id}")

        // 从列表中移除选中的测量
        val removed = measurements.remove(selected)

        if (removed) {
            // 清除选中状态
            selectedMeasurement = null

            // 重置拖拽状态
            isDraggingPoint = false
            isDraggingText = false
            draggedPointIndex = -1

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            }

            // 更新显示
            notifyUpdate()

            Log.d(TAG, "✅ Measurement deleted successfully. Remaining count: ${measurements.size}")
            return true
        } else {
            Log.e(TAG, "❌ Failed to remove measurement from list")
            return false
        }
    }

    /**
     * 🔄 重置触摸状态 - 用于解决触摸状态不一致问题
     */
    fun resetTouchState() {
        isDraggingPoint = false
        isDraggingText = false
        draggedPointIndex = -1
        Log.d(TAG, "🔄 Touch state reset")
    }

    /**
     * � 检测并修复数据丢失问题
     */
    fun checkAndRecoverData(): Boolean {
        if (measurements.isEmpty() && selectedMeasurement == null) {
            Log.d(TAG, "🚨 Data loss detected - measurements and selection are empty")
            return false
        }

        if (selectedMeasurement != null && !measurements.contains(selectedMeasurement)) {
            Log.d(TAG, "🚨 Inconsistent state - selected measurement not in list")
            selectedMeasurement = measurements.firstOrNull()
            Log.d(TAG, "🔄 Recovered by selecting first measurement: ${selectedMeasurement?.id}")
        }

        return true
    }

    /**
     * �📊 通知更新
     */
    private fun notifyUpdate() {
        Log.d(TAG, "🔄 [DEBUG] notifyUpdate called - measurementUpdateCallback: ${measurementUpdateCallback != null}")
        measurementUpdateCallback?.invoke()
    }

    /**
     * 📊 获取测量数量（调试用）
     */
    fun getMeasurementCount(): Int {
        return measurements.size
    }
}

// 坐标转换工具函数 - 完全复制yolo_demo版本
fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
    val matrix = imageView.imageMatrix
    val inverseMatrix = Matrix()

    return if (matrix.invert(inverseMatrix)) {
        val point = FloatArray(2) { 0f }
        point[0] = viewPoint.x
        point[1] = viewPoint.y
        inverseMatrix.mapPoints(point)
        PointF(point[0], point[1])
    } else {
        PointF(viewPoint.x, viewPoint.y)
    }
}

fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
    val matrix = imageView.imageMatrix
    val point = FloatArray(2) { 0f }
    point[0] = bitmapPoint.x
    point[1] = bitmapPoint.y
    matrix.mapPoints(point)
    return PointF(point[0], point[1])
}

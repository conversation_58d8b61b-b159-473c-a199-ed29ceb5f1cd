package com.touptek.measurerealize

import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.utils.AngleMeasureHelper
import com.touptek.measurerealize.TpImageView

/**
 * 🎯 专业级测量触摸处理器
 * 
 * 核心职责：
 * 1. 处理测量模式下的触摸事件分发
 * 2. 协调TpImageView的缩放功能与测量功能
 * 3. 确保触摸事件的正确优先级处理
 * 4. 提供专业级的手势识别体验
 * 
 * 设计理念：
 * - 单点触摸优先给测量功能处理
 * - 多点触摸交给TpImageView处理缩放
 * - 确保两种功能无缝协同工作
 */
class MeasurementTouchHandler {
    
    companion object {
        private const val TAG = "MeasurementTouchHandler"
    }
    
    // 核心组件引用
    private var imageView: TpImageView? = null
    private var angleMeasureHelper: AngleMeasureHelper? = null
    
    // 状态管理
    private var isInitialized = false
    
    /**
     * 🚀 初始化触摸处理器
     */
    fun initialize(imageView: TpImageView, angleMeasureHelper: AngleMeasureHelper) {
        this.imageView = imageView
        this.angleMeasureHelper = angleMeasureHelper
        this.isInitialized = true
        
        Log.d(TAG, "🚀 MeasurementTouchHandler initialized")
    }
    
    /**
     * 🎯 处理测量相关的触摸事件
     * 
     * @param event 触摸事件
     * @param viewWidth 视图宽度
     * @param viewHeight 视图高度
     * @return 是否处理了该事件
     */
    fun handleMeasurementTouch(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ TouchHandler not initialized")
            return false
        }
        
        val helper = angleMeasureHelper ?: return false
        
        try {
            // 记录触摸事件详情
            logTouchEvent(event)
            
            // 只处理单点触摸的测量操作
            if (event.pointerCount == 1) {
                val handled = helper.handleTouchEvent(event, viewWidth, viewHeight)
                
                if (handled) {
                    Log.d(TAG, "✅ Touch event handled by measurement helper")
                    return true
                }
            } else {
                Log.d(TAG, "🔄 Multi-touch detected, delegating to zoom functionality")
            }
            
            return false
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error handling measurement touch: ${e.message}")
            return false
        }
    }
    
    /**
     * 🎨 智能触摸事件分发
     * 
     * 根据触摸类型和当前状态，智能决定事件处理优先级：
     * 1. 单点触摸 + 测量模式 = 优先测量处理
     * 2. 多点触摸 = 交给缩放处理
     * 3. 测量完成后的单点触摸 = 可能是新的测量操作
     */
    fun smartTouchDispatch(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) return false
        
        when (event.pointerCount) {
            1 -> {
                // 单点触摸：优先处理测量
                return handleSingleTouch(event, viewWidth, viewHeight)
            }
            2 -> {
                // 双点触摸：处理缩放手势
                return handleMultiTouch(event)
            }
            else -> {
                // 多点触摸：交给系统处理
                Log.d(TAG, "🔄 Complex multi-touch, delegating to system")
                return false
            }
        }
    }
    
    /**
     * 🎯 处理单点触摸事件
     */
    private fun handleSingleTouch(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        val helper = angleMeasureHelper ?: return false
        
        // 检查是否在测量区域内
        val touchX = event.x
        val touchY = event.y
        
        Log.d(TAG, "🎯 Single touch at ($touchX, $touchY)")
        
        // 交给测量助手处理
        val handled = helper.handleTouchEvent(event, viewWidth, viewHeight)
        
        if (handled) {
            Log.d(TAG, "✅ Single touch handled by measurement")
            
            // 提供触觉反馈（如果需要）
            provideTactileFeedback(event)
        }
        
        return handled
    }
    
    /**
     * 🔄 处理多点触摸事件
     */
    @Suppress("UNUSED_PARAMETER")
    private fun handleMultiTouch(event: MotionEvent): Boolean {
        Log.d(TAG, "🔄 Multi-touch detected, preserving zoom functionality")
        
        // 多点触摸时，确保测量状态不被意外改变
        // 但不阻止缩放功能的正常工作
        return false // 让TpImageView处理缩放
    }
    
    /**
     * 🎨 提供专业级触觉反馈
     */
    private fun provideTactileFeedback(event: MotionEvent) {
        try {
            // 在特定操作完成时提供轻微的触觉反馈
            if (event.action == MotionEvent.ACTION_UP) {
                // 这里可以添加振动反馈或其他触觉提示
                Log.d(TAG, "🎨 Providing tactile feedback for measurement completion")
            }
        } catch (e: Exception) {
            Log.w(TAG, "⚠️ Failed to provide tactile feedback: ${e.message}")
        }
    }
    
    /**
     * 📊 记录触摸事件详情（用于调试）
     */
    private fun logTouchEvent(event: MotionEvent) {
        val action = when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> "DOWN"
            MotionEvent.ACTION_MOVE -> "MOVE"
            MotionEvent.ACTION_UP -> "UP"
            MotionEvent.ACTION_CANCEL -> "CANCEL"
            MotionEvent.ACTION_POINTER_DOWN -> "POINTER_DOWN"
            MotionEvent.ACTION_POINTER_UP -> "POINTER_UP"
            else -> "OTHER(${event.action})"
        }
        
        Log.d(TAG, "📱 Touch: $action at (${event.x}, ${event.y}), pointers: ${event.pointerCount}")
    }
    
    /**
     * 🔄 重置触摸处理器状态
     */
    fun reset() {
        Log.d(TAG, "🔄 Resetting touch handler state")
        // 这里可以重置任何内部状态
    }
    
    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        Log.d(TAG, "🧹 Cleaning up MeasurementTouchHandler")
        
        imageView = null
        angleMeasureHelper = null
        isInitialized = false
    }
    
    /**
     * 📊 获取处理器状态信息
     */
    fun getStatusInfo(): String {
        return "MeasurementTouchHandler(initialized=$isInitialized, " +
                "imageView=${imageView != null}, " +
                "helper=${angleMeasureHelper != null})"
    }
}

package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🔄 四点角度测量数据
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\f\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B%\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0007\u001a\u00020\b\u00a2\u0006\u0002\u0010\tJ\u000f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\bH\u00c6\u0003J-\u0010\u0012\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\b2\b\u0010\u0014\u001a\u0004\u0018\u00010\u0015H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0019H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\fR\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u001a"}, d2 = {"Lcom/touptek/measurerealize/utils/FourPointAngleMeasurementData;", "Lcom/touptek/measurerealize/utils/MeasurementData;", "points", "", "Landroid/graphics/PointF;", "angle", "", "isDragging", "", "(Ljava/util/List;DZ)V", "getAngle", "()D", "()Z", "getPoints", "()Ljava/util/List;", "component1", "component2", "component3", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
public final class FourPointAngleMeasurementData extends com.touptek.measurerealize.utils.MeasurementData {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<android.graphics.PointF> points = null;
    private final double angle = 0.0;
    private final boolean isDragging = false;
    
    /**
     * 🔄 四点角度测量数据
     */
    @org.jetbrains.annotations.NotNull
    public final com.touptek.measurerealize.utils.FourPointAngleMeasurementData copy(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double angle, boolean isDragging) {
        return null;
    }
    
    /**
     * 🔄 四点角度测量数据
     */
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    /**
     * 🔄 四点角度测量数据
     */
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    /**
     * 🔄 四点角度测量数据
     */
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public java.lang.String toString() {
        return null;
    }
    
    public FourPointAngleMeasurementData(@org.jetbrains.annotations.NotNull
    java.util.List<? extends android.graphics.PointF> points, double angle, boolean isDragging) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<android.graphics.PointF> getPoints() {
        return null;
    }
    
    public final double component2() {
        return 0.0;
    }
    
    public final double getAngle() {
        return 0.0;
    }
    
    public final boolean component3() {
        return false;
    }
    
    public final boolean isDragging() {
        return false;
    }
}
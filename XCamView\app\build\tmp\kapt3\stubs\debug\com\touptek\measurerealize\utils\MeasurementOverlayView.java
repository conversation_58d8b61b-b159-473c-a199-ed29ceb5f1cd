package com.touptek.measurerealize.utils;

import java.lang.System;

/**
 * 🎨 专业级测量覆盖层 - 超越iPad体验的可视化
 *
 * 核心特性：
 * - 多层测量渲染：支持同时显示多种测量类型
 * - 专业视觉效果：动态高亮、流畅动画、精美渲染
 * - 智能坐标转换：与TpImageView完美协作
 * - 高性能绘制：优化的Canvas绘制算法
 */
@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\u00a2\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\u0018\u00002\u00020\u0001B%\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u0015\u001a\u00020\u0016J0\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001c\u001a\u00020\u001b2\u0006\u0010\u001d\u001a\u00020\u001b2\u0006\u0010\u001e\u001a\u00020\u001fH\u0002J \u0010 \u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u00020\"2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J(\u0010#\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u001b2\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010$\u001a\u00020%H\u0002J \u0010&\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u00020\'2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u0010(\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u00020)2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u0010*\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u00020+2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u0010,\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u00020-2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u0010.\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u00020/2\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u00100\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u0002012\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u00102\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u0002032\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J \u00104\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u00192\u0006\u0010!\u001a\u0002052\u0006\u0010\u000e\u001a\u00020\u000fH\u0002J\u0010\u00106\u001a\u00020\u00162\u0006\u00107\u001a\u000208H\u0002J\u0010\u00109\u001a\u00020\u00162\u0006\u0010\u0018\u001a\u00020\u0019H\u0014J\u0010\u0010:\u001a\u00020%2\u0006\u00107\u001a\u000208H\u0016J\u000e\u0010;\u001a\u00020\u00162\u0006\u0010<\u001a\u00020\nJ\u000e\u0010=\u001a\u00020\u00162\u0006\u0010\u000e\u001a\u00020\u000fJ\u0010\u0010>\u001a\u00020\u00162\b\u0010!\u001a\u0004\u0018\u00010\u0012R\u0010\u0010\t\u001a\u0004\u0018\u00010\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\r\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006?"}, d2 = {"Lcom/touptek/measurerealize/utils/MeasurementOverlayView;", "Landroid/view/View;", "context", "Landroid/content/Context;", "attrs", "Landroid/util/AttributeSet;", "defStyleAttr", "", "(Landroid/content/Context;Landroid/util/AttributeSet;I)V", "angleMeasureHelper", "Lcom/touptek/measurerealize/utils/AngleMeasureHelper;", "arcPaint", "Landroid/graphics/Paint;", "highlightPointPaint", "imageView", "Lcom/touptek/measurerealize/TpImageView;", "linePaint", "measurementData", "Lcom/touptek/measurerealize/utils/MeasurementData;", "pointPaint", "textPaint", "clearMeasurement", "", "drawAngleArc", "canvas", "Landroid/graphics/Canvas;", "vertex", "Landroid/graphics/PointF;", "point1", "point2", "angle", "", "drawAngleMeasurement", "data", "Lcom/touptek/measurerealize/utils/AngleMeasurementData;", "drawAngleText", "isDragging", "", "drawCircleMeasurement", "Lcom/touptek/measurerealize/utils/CircleMeasurementData;", "drawDistanceMeasurement", "Lcom/touptek/measurerealize/utils/DistanceMeasurementData;", "drawEllipseMeasurement", "Lcom/touptek/measurerealize/utils/EllipseMeasurementData;", "drawFourPointAngleMeasurement", "Lcom/touptek/measurerealize/utils/FourPointAngleMeasurementData;", "drawMultiPointPathMeasurement", "Lcom/touptek/measurerealize/utils/MultiPointPathMeasurementData;", "drawParallelLinesMeasurement", "Lcom/touptek/measurerealize/utils/ParallelLinesMeasurementData;", "drawRectangleMeasurement", "Lcom/touptek/measurerealize/utils/RectangleMeasurementData;", "drawThreePointCircleMeasurement", "Lcom/touptek/measurerealize/utils/ThreePointCircleMeasurementData;", "forwardEventToImageView", "event", "Landroid/view/MotionEvent;", "onDraw", "onTouchEvent", "setAngleMeasureHelper", "helper", "setImageView", "setMeasurementData", "app_debug"})
public final class MeasurementOverlayView extends android.view.View {
    private final android.graphics.Paint linePaint = null;
    private final android.graphics.Paint pointPaint = null;
    private final android.graphics.Paint highlightPointPaint = null;
    private final android.graphics.Paint textPaint = null;
    private final android.graphics.Paint arcPaint = null;
    private com.touptek.measurerealize.utils.MeasurementData measurementData;
    private com.touptek.measurerealize.TpImageView imageView;
    private com.touptek.measurerealize.utils.AngleMeasureHelper angleMeasureHelper;
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs) {
        super(null);
    }
    
    @kotlin.jvm.JvmOverloads
    public MeasurementOverlayView(@org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.Nullable
    android.util.AttributeSet attrs, int defStyleAttr) {
        super(null);
    }
    
    public final void setImageView(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.TpImageView imageView) {
    }
    
    public final void setAngleMeasureHelper(@org.jetbrains.annotations.NotNull
    com.touptek.measurerealize.utils.AngleMeasureHelper helper) {
    }
    
    public final void setMeasurementData(@org.jetbrains.annotations.Nullable
    com.touptek.measurerealize.utils.MeasurementData data) {
    }
    
    public final void clearMeasurement() {
    }
    
    @java.lang.Override
    protected void onDraw(@org.jetbrains.annotations.NotNull
    android.graphics.Canvas canvas) {
    }
    
    /**
     * 🎯 绘制角度测量 - 专业级三点角度可视化
     */
    private final void drawAngleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.AngleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎨 绘制角度弧线 - 专业级弧线渲染
     */
    private final void drawAngleArc(android.graphics.Canvas canvas, android.graphics.PointF vertex, android.graphics.PointF point1, android.graphics.PointF point2, double angle) {
    }
    
    /**
     * 🎨 绘制角度文本 - 智能位置和专业样式
     */
    private final void drawAngleText(android.graphics.Canvas canvas, android.graphics.PointF vertex, double angle, boolean isDragging) {
    }
    
    private final void drawDistanceMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.DistanceMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawRectangleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.RectangleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawCircleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.CircleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawThreePointCircleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ThreePointCircleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawFourPointAngleMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.FourPointAngleMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawParallelLinesMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.ParallelLinesMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawEllipseMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.EllipseMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    private final void drawMultiPointPathMeasurement(android.graphics.Canvas canvas, com.touptek.measurerealize.utils.MultiPointPathMeasurementData data, com.touptek.measurerealize.TpImageView imageView) {
    }
    
    /**
     * 🎯 处理触摸事件 - 委托给测量助手
     */
    @java.lang.Override
    public boolean onTouchEvent(@org.jetbrains.annotations.NotNull
    android.view.MotionEvent event) {
        return false;
    }
    
    /**
     * 🔄 将事件转发给TpImageView
     */
    private final void forwardEventToImageView(android.view.MotionEvent event) {
    }
}